import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Clock, Users, AlertCircle } from "lucide-react";
import type { Activity } from "@/types/booking";

interface ActivitySelectorProps {
  activities: Activity[];
  selectedActivities: Activity[];
  onActivityToggle: (activity: Activity) => void;
  guestCount: number;
}

export function ActivitySelector({
  activities,
  selectedActivities,
  onActivityToggle,
  guestCount,
}: ActivitySelectorProps) {
  const [selectedCategory, setSelectedCategory] = useState<string>("all");

  const categories = [
    { id: "all", name: "All Activities" },
    { id: "safari", name: "Safari Adventures" },
    { id: "cultural", name: "Cultural Experiences" },
    { id: "adventure", name: "Adventure Activities" },
    { id: "relaxation", name: "Relaxation & Wellness" },
  ];

  const filteredActivities = activities.filter(
    (activity) =>
      selectedCategory === "all" || activity.category === selectedCategory
  );

  const isActivitySelected = (activityId: string) =>
    selectedActivities.some((activity) => activity.id === activityId);

  const getTotalActivitiesPrice = () =>
    selectedActivities.reduce(
      (total, activity) => total + activity.price * guestCount,
      0
    );

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case "safari":
        return "🦁";
      case "cultural":
        return "🏛️";
      case "adventure":
        return "🏔️";
      case "relaxation":
        return "🧘";
      default:
        return "🌟";
    }
  };

  return (
    <div className="space-y-6">
      {/* Category Filter */}
      <div className="flex flex-wrap gap-2">
        {categories.map((category) => (
          <Button
            key={category.id}
            variant={selectedCategory === category.id ? "default" : "outline"}
            size="sm"
            onClick={() => setSelectedCategory(category.id)}
            className={
              selectedCategory === category.id
                ? "bg-[#2C5530] hover:bg-[#8B4513]"
                : "border-[#2C5530] text-[#2C5530] hover:bg-[#2C5530] hover:text-white"
            }
          >
            {category.name}
          </Button>
        ))}
      </div>

      {/* Selected Activities Summary */}
      {selectedActivities.length > 0 && (
        <Card className="border-[#DAA520] bg-[#DAA520]/5">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg text-[#2C5530]">
              Selected Activities ({selectedActivities.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {selectedActivities.map((activity) => (
                <div
                  key={activity.id}
                  className="flex justify-between items-center text-sm"
                >
                  <span>{activity.name}</span>
                  <span className="font-semibold text-[#8B4513]">
                    ${activity.price} × {guestCount} = $
                    {activity.price * guestCount}
                  </span>
                </div>
              ))}
              <div className="border-t pt-2 flex justify-between items-center font-bold text-[#2C5530]">
                <span>Total Activities Cost:</span>
                <span>${getTotalActivitiesPrice()}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Activities Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {filteredActivities.map((activity) => {
          const isSelected = isActivitySelected(activity.id);
          const isOverCapacity =
            activity.maxParticipants && guestCount > activity.maxParticipants;

          return (
            <Card
              key={activity.id}
              className={`transition-all duration-300 cursor-pointer hover:shadow-lg ${
                isSelected
                  ? "border-[#2C5530] bg-[#2C5530]/5"
                  : "border-neutral-200 hover:border-[#DAA520]"
              } ${isOverCapacity ? "opacity-60" : ""}`}
              onClick={() => !isOverCapacity && onActivityToggle(activity)}
            >
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-3">
                    <Checkbox
                      checked={isSelected}
                      disabled={isOverCapacity}
                      className="data-[state=checked]:bg-[#2C5530] data-[state=checked]:border-[#2C5530]"
                    />
                    <div>
                      <CardTitle className="text-lg text-[#2C5530] flex items-center gap-2">
                        <span>{getCategoryIcon(activity.category)}</span>
                        {activity.name}
                      </CardTitle>
                      <div className="flex items-center gap-4 mt-2">
                        <Badge
                          variant="secondary"
                          className="bg-[#DAA520]/20 text-[#DAA520] capitalize"
                        >
                          {activity.category}
                        </Badge>
                        <div className="flex items-center gap-1 text-sm text-neutral-600">
                          <Clock className="h-4 w-4" />
                          {activity.duration}
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-xl font-bold text-[#8B4513]">
                      ${activity.price}
                    </div>
                    <div className="text-sm text-neutral-600">per person</div>
                  </div>
                </div>
              </CardHeader>

              <CardContent>
                <p className="text-neutral-600 text-sm mb-4">
                  {activity.description}
                </p>

                <div className="space-y-2 text-sm">
                  {activity.maxParticipants && (
                    <div className="flex items-center gap-2">
                      <Users className="h-4 w-4 text-[#2C5530]" />
                      <span>Max {activity.maxParticipants} participants</span>
                      {isOverCapacity && (
                        <Badge variant="destructive" className="ml-auto">
                          Exceeds capacity
                        </Badge>
                      )}
                    </div>
                  )}

                  {activity.ageRestriction && (
                    <div className="flex items-center gap-2">
                      <AlertCircle className="h-4 w-4 text-[#DAA520]" />
                      <span>{activity.ageRestriction}</span>
                    </div>
                  )}
                </div>

                {isOverCapacity && (
                  <div className="mt-3 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-700">
                    This activity has a maximum of {activity.maxParticipants}{" "}
                    participants. Your group has {guestCount} people.
                  </div>
                )}
              </CardContent>
            </Card>
          );
        })}
      </div>

      {filteredActivities.length === 0 && (
        <div className="text-center py-8 text-neutral-500">
          No activities found in this category.
        </div>
      )}
    </div>
  );
}
