import { useNavigate } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { AspectRatio } from "@/components/ui/aspect-ratio";
import { Badge } from "@/components/ui/badge";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { accommodations } from "@/data/accommodations";
import { Users, Bed, Wifi, Coffee, Shield, Car } from "lucide-react";

const Accommodations = () => {
  const navigate = useNavigate();

  const handleBookNow = (accommodationId: string) => {
    navigate(`/booking?accommodation=${accommodationId}`);
  };

  const getIconForAmenity = (amenity: string) => {
    if (amenity.toLowerCase().includes("wifi"))
      return <Wifi className="h-4 w-4" />;
    if (
      amenity.toLowerCase().includes("coffee") ||
      amenity.toLowerCase().includes("tea")
    )
      return <Coffee className="h-4 w-4" />;
    if (amenity.toLowerCase().includes("safe"))
      return <Shield className="h-4 w-4" />;
    if (amenity.toLowerCase().includes("parking"))
      return <Car className="h-4 w-4" />;
    return null;
  };

  return (
    <div className="flex flex-col w-full min-h-screen">
      {/* Hero Section */}
      <section className="relative w-full h-screen bg-gradient-to-r from-[#0d1a0e] to-[#3a1c07]">
        <div className="absolute inset-0">
          <img
            src="/images/accommodations-hero.jpg"
            alt="Malombo Accommodations"
            className="w-full h-full object-cover opacity-30"
          />
        </div>
        <div className="relative z-10 container mx-auto h-full flex flex-col justify-center px-4">
          {/* Breadcrumb */}
          <div className="mb-1">
            <Breadcrumb>
              <BreadcrumbList className="text-white/80">
                <BreadcrumbItem>
                  <BreadcrumbLink
                    onClick={() => navigate("/")}
                    className="text-white/80 hover:text-white cursor-pointer"
                  >
                    Home
                  </BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator className="text-white/60" />
                <BreadcrumbItem>
                  <BreadcrumbPage className="text-white font-semibold">
                    Accommodations
                  </BreadcrumbPage>
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>

          <h1 className="text-5xl md:text-6xl text-center mb-4 playfair-display-heading text-white">
            Our <span className="text-[#DAA520]">Accommodations</span>
          </h1>
          <p className="text-xl md:text-2xl text-center mb-8 max-w-3xl mx-auto playfair-display-subheading text-white/90">
            Choose from our carefully designed accommodations, each offering a
            unique way to experience the African wilderness
          </p>
        </div>
      </section>

      {/* Accommodations Grid */}
      <section className="py-16 w-[90%] mx-auto">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 lg:grid-cols-1 gap-12">
            {accommodations.map((accommodation, index) => (
              <Card
                key={accommodation.id}
                className={`overflow-hidden group hover:shadow-2xl transition-all duration-500 bg-white/95 backdrop-blur-sm border-t-4 ${
                  index % 2 === 0 ? "border-t-[#2C5530]" : "border-t-[#DAA520]"
                }`}
              >
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-0">
                  {/* Image Section */}
                  <div className={`${index % 2 === 1 ? "lg:order-2" : ""}`}>
                    <AspectRatio ratio={16 / 12}>
                      <img
                        src={accommodation.image}
                        alt={accommodation.title}
                        className="object-cover w-full h-full transition-transform duration-500 group-hover:scale-105"
                      />
                    </AspectRatio>
                  </div>

                  {/* Content Section */}
                  <div
                    className={`p-8 flex flex-col justify-between ${
                      index % 2 === 1 ? "lg:order-1" : ""
                    }`}
                  >
                    <div>
                      <div className="flex items-center justify-between mb-4">
                        <Badge
                          variant="secondary"
                          className={`${
                            accommodation.category === "luxury"
                              ? "bg-[#DAA520]/20 text-[#DAA520]"
                              : accommodation.category === "premium"
                              ? "bg-[#8B4513]/20 text-[#8B4513]"
                              : "bg-[#2C5530]/20 text-[#2C5530]"
                          } capitalize font-semibold`}
                        >
                          {accommodation.category}
                        </Badge>
                        <div className="text-right">
                          <div className="text-2xl font-bold text-[#2C5530]">
                            ${accommodation.pricePerNight}
                          </div>
                          <div className="text-sm text-neutral-600">
                            per night
                          </div>
                        </div>
                      </div>

                      <CardHeader className="p-0 mb-4">
                        <CardTitle className="playfair-display-heading text-3xl font-bold text-[#2C5530] mb-2">
                          {accommodation.title}
                        </CardTitle>
                        <CardDescription className="text-base text-neutral-600 leading-relaxed">
                          {accommodation.detailedDescription}
                        </CardDescription>
                      </CardHeader>

                      {/* Key Details */}
                      <div className="grid grid-cols-2 gap-4 mb-6 p-4 bg-neutral-50 rounded-lg">
                        <div className="flex items-center gap-2">
                          <Users className="h-5 w-5 text-[#2C5530]" />
                          <span className="text-sm">
                            Up to {accommodation.maxGuests} guests
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Bed className="h-5 w-5 text-[#2C5530]" />
                          <span className="text-sm">
                            {accommodation.bedConfiguration}
                          </span>
                        </div>
                        <div className="col-span-2 text-sm text-neutral-600">
                          <strong>Size:</strong> {accommodation.size}
                        </div>
                      </div>

                      {/* Amenities */}
                      <div className="mb-6">
                        <h4 className="font-semibold text-[#2C5530] mb-3">
                          Key Amenities
                        </h4>
                        <div className="grid grid-cols-1 gap-2">
                          {accommodation.amenities
                            .slice(0, 6)
                            .map((amenity, idx) => (
                              <div
                                key={idx}
                                className="flex items-center gap-2 text-sm text-neutral-600"
                              >
                                {getIconForAmenity(amenity) || (
                                  <div className="w-2 h-2 bg-[#DAA520] rounded-full" />
                                )}
                                <span>{amenity}</span>
                              </div>
                            ))}
                          {accommodation.amenities.length > 6 && (
                            <div className="text-sm text-[#8B4513] font-medium">
                              +{accommodation.amenities.length - 6} more
                              amenities
                            </div>
                          )}
                        </div>
                      </div>
                    </div>

                    <CardFooter className="p-0 pt-4 border-t border-neutral-200">
                      <Button
                        size="lg"
                        className="w-full bg-gradient-to-r from-[#2C5530] to-[#8B4513] hover:from-[#8B4513] hover:to-[#2C5530] text-white transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
                        onClick={() => handleBookNow(accommodation.id)}
                      >
                        Book Now - ${accommodation.pricePerNight}/night
                      </Button>
                    </CardFooter>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-16 bg-gradient-to-r from-[#2C5530]/10 to-[#8B4513]/10 w-[90%] mx-auto">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl playfair-display-heading text-[#2C5530] mb-4">
            Need Help Choosing?
          </h2>
          <p className="text-lg text-neutral-600 mb-8 max-w-2xl mx-auto">
            Our safari experts are here to help you select the perfect
            accommodation for your African adventure.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              variant="outline"
              size="lg"
              className="border-[#2C5530] text-[#2C5530] hover:bg-[#2C5530] hover:text-white"
            >
              Contact Our Experts
            </Button>
            <Button
              size="lg"
              className="bg-gradient-to-r from-[#DAA520] to-[#8B4513] hover:from-[#8B4513] hover:to-[#DAA520] text-white"
              onClick={() => navigate("/booking")}
            >
              Start Booking Process
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Accommodations;
