import { BrowserRouter as Router, Routes, Route } from "react-router-dom";
import Home from "./pages/Home";
import Accommodations from "./pages/Accommodations";
import Booking from "./pages/Booking";
import "./App.css";
import { RootLayout } from "./layouts/RootLayout";

function App() {
  return (
    <Router>
      <RootLayout>
        <Routes>
          <Route path="/" element={<Home />} />
          <Route path="/accommodations" element={<Accommodations />} />
          <Route path="/booking" element={<Booking />} />
        </Routes>
      </RootLayout>
    </Router>
  );
}

export default App;
